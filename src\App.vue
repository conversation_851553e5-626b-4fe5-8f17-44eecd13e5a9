<template>
  <div id="app">
    <!-- 页面内容 -->
    <router-view v-if="$route" />

    <!-- 自定义tabbar -->
    <custom-tabbar ref="customTabbar" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { isTabbarPage } from '@/config/tabbar.js'
import { initTabbar } from '@/utils/tabbar.js'

export default {
  data() {
    return {
      isTabbarPageFlag: false
    }
  },

  computed: {
    ...mapGetters(['domainName'])
  },

  watch: {
    // 监听租户变化，重新初始化tabbar
    domainName: {
      handler(newVal) {
        if (newVal) {
          this.initTabbarSettings()
        }
      },
      immediate: true
    }
  },

  onLaunch: function() {
    console.log('App Launch')
    // 租户信息初始化已在router/index.js中处理
  },

  onShow: function() {
    console.log('App Show')
    this.checkTabbarVisibility()

    // 监听页面切换事件
    uni.$on('pageChange', () => {
      this.$nextTick(() => {
        this.checkTabbarVisibility()
      })
    })
  },

  onHide: function() {
    console.log('App Hide')
    // 移除页面切换事件监听
    uni.$off('pageChange')
  },

  methods: {

    // 初始化tabbar设置
    initTabbarSettings() {
      if (this.domainName) {
        initTabbar(this.domainName)
      }
    },

    // 检查tabbar显示状态
    checkTabbarVisibility() {
      try {
        console.log('检查tabbar显示状态')
        const pages = getCurrentPages()
        console.log('当前页面数量:', pages.length)

        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          const route = currentPage.route
          console.log('当前路由:', route)
          console.log('当前domainName:', this.domainName)
					console.warn('当前路由:', route);
          this.isTabbarPageFlag = isTabbarPage(route, this.domainName || '')
          console.log('是否为tabbar页面:', this.isTabbarPageFlag)

          // 控制tabbar显示/隐藏
          if (this.$refs.customTabbar) {
            if (this.isTabbarPageFlag) {
              console.log('显示tabbar')
              this.$refs.customTabbar.show()
            } else {
              console.log('隐藏tabbar')
              this.$refs.customTabbar.hide()
            }
          } else {
            console.log('customTabbar ref不存在')
          }
        } else {
          // 页面栈为空时，延迟重试
          console.log('页面栈为空，延迟重试')
          setTimeout(() => {
            this.checkTabbarVisibility()
          }, 100)
        }
      } catch (error) {
        console.error('检查tabbar显示状态失败:', error)
        console.error('错误堆栈:', error.stack)
        // 出错时默认隐藏tabbar
        if (this.$refs.customTabbar) {
          this.$refs.customTabbar.hide()
        }
      }
    }
  }
}
</script>

<style>
/* 全局样式 */
#app {
  height: 100vh;
  position: relative;
}

/* 为tabbar页面添加底部padding */
.tabbar-page {
  padding-bottom: calc(55px + env(safe-area-inset-bottom)) !important;
}
</style>