<!--
 * @Description: 自定义动态tabbar组件
 * @Author: AI Assistant
 * @Date: 2025-07-17
-->
<template>
  <view class="custom-tabbar" :style="tabbarStyle" v-if="showTabbar">
    <view 
      v-for="(item, index) in tabbarList" 
      :key="item.key"
      class="tabbar-item"
      :class="{ 'active': currentIndex === index }"
      @click="switchTab(item, index)"
    >
      <image 
        class="tabbar-icon" 
        :src="currentIndex === index ? item.selectedIconPath : item.iconPath"
        :style="iconStyle"
      />
      <text 
        class="tabbar-text" 
        :style="currentIndex === index ? selectedTextStyle : textStyle"
      >
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTabbarConfig, getTabbarIndex } from '@/config/tabbar.js'

export default {
  name: 'CustomTabbar',
  data() {
    return {
      currentIndex: 0,
      tabbarConfig: {},
      showTabbar: true
    }
  },
  
  computed: {
    ...mapGetters(['domainName']),
    
    // 根据租户获取tabbar列表
    tabbarList() {
      return this.tabbarConfig.list || []
    },
    
    // tabbar样式
    tabbarStyle() {
      return {
        backgroundColor: this.tabbarConfig.backgroundColor || '#ffffff',
        borderTopColor: this.tabbarConfig.borderStyle || '#e5e5e5',
        height: this.tabbarConfig.height || '55px'
      }
    },
    
    // 图标样式
    iconStyle() {
      return {
        width: this.tabbarConfig.iconWidth || '22px',
        height: this.tabbarConfig.iconWidth || '22px'
      }
    },
    
    // 文字样式
    textStyle() {
      return {
        color: this.tabbarConfig.color || '#000000'
      }
    },
    
    // 选中文字样式
    selectedTextStyle() {
      return {
        color: this.tabbarConfig.selectedColor || '#000000'
      }
    },

    // 判断当前页面是否应该显示tabbar
    shouldShowTabbar() {
      try {
        const pages = getCurrentPages()
        console.log('shouldShowTabbar检查 - 页面栈长度:', pages.length)

        if (pages.length === 0) {
          console.log('页面栈为空，不显示tabbar')
          return false
        }

        if (!this.tabbarList || this.tabbarList.length === 0) {
          console.log('tabbar配置为空，不显示tabbar')
          return false
        }

        const currentPage = pages[pages.length - 1]
        const route = currentPage.route
        console.log('当前页面路由:', route)
        console.log('tabbar页面列表:', this.tabbarList.map(tab => tab.pagePath))

        // 检查当前页面是否在tabbar配置中 - 使用更灵活的匹配方式
        const shouldShow = this.tabbarList.some(tab => {
          // 完全匹配
          if (tab.pagePath === route) return true

          // 去掉前缀的匹配
          if (route.endsWith(tab.pagePath)) return true

          // 去掉index的匹配
          if (tab.pagePath.endsWith('/index') && route === tab.pagePath.replace('/index', '')) return true

          return false
        })

        console.log('是否应该显示tabbar:', shouldShow)
        return shouldShow
      } catch (error) {
        console.error('判断tabbar显示状态失败:', error)
        return false
      }
    }
  },
  
  watch: {
    // 监听租户变化，重新加载tabbar配置
    domainName: {
      handler(newVal, oldVal) {
        console.log('domainName变化:', { oldVal, newVal })
        if (newVal) {
          this.loadTabbarConfig()
        } else {
          // 如果domainName为空，使用默认配置
          console.log('domainName为空，使用默认配置')
          this.loadTabbarConfig()
        }
      },
      immediate: true
    }
  },
  
  mounted() {
    this.loadTabbarConfig()
    this.updateCurrentIndex()

    // 监听tabbar索引更新事件
    uni.$on('updateTabbarIndex', (index) => {
      if (index >= 0) {
        this.currentIndex = index
      }
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('updateTabbarIndex')
  },

  mounted() {
    console.log('tabbar组件mounted, domainName:', this.domainName)
    // 确保组件挂载后加载配置
    this.loadTabbarConfig()

    // 延迟检查页面状态，确保页面栈已初始化
    this.$nextTick(() => {
      setTimeout(() => {
        this.updateCurrentIndex()
      }, 100)
    })
  },

  methods: {
    // 加载tabbar配置
    loadTabbarConfig() {
      try {
        console.log('开始加载tabbar配置, domainName:', this.domainName)
        const config = getTabbarConfig(this.domainName || '')
        console.log('获取到的tabbar配置:', config)
        this.tabbarConfig = config
        this.updateCurrentIndex()
      } catch (error) {
        console.error('加载tabbar配置失败:', error)
        console.error('错误堆栈:', error.stack)
        // 使用默认配置
        this.tabbarConfig = {
          list: [
            {
              pagePath: "pages/index",
              iconPath: "static/images/tabbar/find.png",
              selectedIconPath: "static/images/tabbar/find_active.png",
              text: "发现",
              key: "discover"
            }
          ],
          color: '#000000',
          selectedColor: '#000000',
          backgroundColor: '#ffffff',
          height: '55px'
        }
      }
    },
    
    // 更新当前选中索引
    updateCurrentIndex() {
      try {
        const pages = getCurrentPages()
        console.log('updateCurrentIndex - 当前页面栈:', pages.length)
        if (pages.length > 0) {
          const currentPage = pages[pages.length - 1]
          const route = currentPage.route
          console.log('updateCurrentIndex - 当前页面路由:', route)
          console.log('updateCurrentIndex - 当前页面完整信息:', currentPage)
          console.log('updateCurrentIndex - domainName:', this.domainName)
          console.log('updateCurrentIndex - tabbarList:', this.tabbarList.map(tab => tab.pagePath))

          const index = getTabbarIndex(route, this.domainName || '')
          console.log('updateCurrentIndex - 计算得到的tabbar索引:', index)
          this.currentIndex = index >= 0 ? index : 0
          console.log('updateCurrentIndex - 设置的currentIndex:', this.currentIndex)
        } else {
          console.log('updateCurrentIndex - 页面栈为空，延迟重试')
          setTimeout(() => {
            this.updateCurrentIndex()
          }, 200)
        }
      } catch (error) {
        console.error('更新tabbar索引失败:', error)
        console.error('错误堆栈:', error.stack)
        this.currentIndex = 0
      }
    },
    
    // 切换tab
    switchTab(item, index) {
      try {
        if (this.currentIndex === index) return

        console.log('切换tab:', { item, index })
        this.currentIndex = index

        // 使用uni.switchTab进行页面跳转
        uni.switchTab({
          url: `/${item.pagePath}`,
          success: () => {
            console.log('switchTab成功:', item.pagePath)
          },
          fail: (err) => {
            console.error('切换tabbar失败:', err)
            // 如果switchTab失败，尝试使用reLaunch
            uni.reLaunch({
              url: `/${item.pagePath}`,
              fail: (reLaunchErr) => {
                console.error('reLaunch也失败:', reLaunchErr)
              }
            })
          }
        })
      } catch (error) {
        console.error('切换tab出错:', error)
      }
    },
    
    // 显示tabbar
    show() {
      this.showTabbar = true
    },
    
    // 隐藏tabbar
    hide() {
      this.showTabbar = false
    },
    
    // 设置当前选中项
    setCurrentIndex(index) {
      this.currentIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  border-top: 1px solid #e5e5e5;
  background-color: #ffffff;
  z-index: 1000; /* 确保tabbar在页面内容之上，但在popup之下 */
  padding-bottom: env(safe-area-inset-bottom);
  
  .tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8rpx 0;
    position: relative;
    
    .tabbar-icon {
      width: 44rpx;
      height: 44rpx;
      margin-bottom: 4rpx;
    }
    
    .tabbar-text {
      font-size: 20rpx;
      line-height: 1;
    }
    
    &.active {
      .tabbar-text {
        font-weight: 500;
      }
    }
  }
}

// 为页面内容添加底部padding，避免被tabbar遮挡
.page-content {
  padding-bottom: calc(55px + env(safe-area-inset-bottom));
}
</style>
