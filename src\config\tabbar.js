/*
 * @Description: tabbar配置文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-07-17 15:00:00
 * @LastEditTime: 2025-07-17 15:40:26
 */


// 默认tabbar配置
const defaultTabbarConfig = {
  color: "#000000",
  selectedColor: "#000000",
  borderStyle: "black",
  backgroundColor: "#ffffff",
  iconWidth: "22px",
  spacing: "0px",
  height: "55px",
  list: [
    {
      pagePath: "pages/index",
      iconPath: "static/images/tabbar/find.png",
      selectedIconPath: "static/images/tabbar/find_active.png",
      text: "发现",
      key: "discover"
    },
    {
      pagePath: "pages/course/index",
      iconPath: "static/images/tabbar/course.png",
      selectedIconPath: "static/images/tabbar/course_active.png",
      text: "课程",
      key: "course"
    },
    {
      pagePath: "pages/learn/index",
      iconPath: "static/images/tabbar/study.png",
      selectedIconPath: "static/images/tabbar/study_active.png",
      text: "学习",
      key: "learn"
    },
    {
      pagePath: "pages/game/index",
      iconPath: "static/images/tabbar/game.png",
      selectedIconPath: "static/images/tabbar/game_active.png",
      text: "游戏",
      key: "game"
    },
    {
      pagePath: "pages/mine/index",
      iconPath: "static/images/tabbar/mine.png",
      selectedIconPath: "static/images/tabbar/mine_active.png",
      text: "我的",
      key: "mine"
    }
  ]
}

// 租户特定的tabbar配置规则
const tenantTabbarRules = {
  // pk租户隐藏游戏模块
  'pk': {
    hiddenTabs: ['game']
  },
  // 可以继续添加其他租户的规则
  // 'other-tenant': {
  //   hiddenTabs: ['course'],
  //   customTabs: [...]
  // }
}

/**
 * 根据租户信息获取tabbar配置
 * @param {string} domainName 租户域名
 * @returns {object} 过滤后的tabbar配置
 */
export function getTabbarConfig(domainName) {
  const config = JSON.parse(JSON.stringify(defaultTabbarConfig))
  
  // 获取租户规则
  const tenantRule = tenantTabbarRules[domainName]
  
  if (tenantRule) {
    // 处理隐藏的tabs
    if (tenantRule.hiddenTabs && tenantRule.hiddenTabs.length > 0) {
      config.list = config.list.filter(tab => !tenantRule.hiddenTabs.includes(tab.key))
    }
    
    // 可以扩展：处理自定义tabs
    if (tenantRule.customTabs) {
      // 添加自定义tabs逻辑
    }
  }
  
  return config
}

/**
 * 检查指定页面是否在当前租户的tabbar中
 * @param {string} pagePath 页面路径
 * @param {string} domainName 租户域名
 * @returns {boolean}
 */
export function isTabbarPage(pagePath, domainName) {
  const config = getTabbarConfig(domainName)
  return config.list.some(tab => tab.pagePath === pagePath)
}

/**
 * 获取当前页面在tabbar中的索引
 * @param {string} pagePath 页面路径
 * @param {string} domainName 租户域名
 * @returns {number} 索引，-1表示不在tabbar中
 */
export function getTabbarIndex(pagePath, domainName) {
  const config = getTabbarConfig(domainName)
  return config.list.findIndex(tab => tab.pagePath === pagePath)
}

export default {
  getTabbarConfig,
  isTabbarPage,
  getTabbarIndex,
  defaultTabbarConfig,
  tenantTabbarRules
}
