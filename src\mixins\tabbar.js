/*
 * @Description: tabbar页面混入
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */

import { mapGetters } from 'vuex'
import { isTabbarPage, getTabbarIndex } from '@/config/tabbar.js'

export default {
  computed: {
    ...mapGetters(['domainName']),
    
    // 判断当前页面是否为tabbar页面
    isCurrentTabbarPage() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route
        return isTabbarPage(route, this.domainName)
      }
      return false
    }
  },
  
  onShow() {
    // 触发页面切换事件
    uni.$emit('pageChange')

    // 如果是tabbar页面，更新tabbar状态
    if (this.isCurrentTabbarPage) {
      this.updateTabbarState()
    }
  },
  
  methods: {
    // 更新tabbar状态
    updateTabbarState() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        const route = currentPage.route
        const currentIndex = getTabbarIndex(route, this.domainName)
        
        // 通过事件通知tabbar组件更新状态
        uni.$emit('updateTabbarIndex', currentIndex)
      }
    },
    
    // 跳转到tabbar页面
    switchToTab(pagePath) {
      uni.switchTab({
        url: `/${pagePath}`,
        fail: (err) => {
          console.error('跳转tabbar页面失败:', err)
          // 如果switchTab失败，尝试使用reLaunch
          uni.reLaunch({
            url: `/${pagePath}`
          })
        }
      })
    }
  }
}
