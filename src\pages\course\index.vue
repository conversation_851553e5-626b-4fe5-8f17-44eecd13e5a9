<!--
 * @Description: 课程tab
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-25 17:09:38
 * @LastEditTime: 2025-03-04 13:54:16
-->

<template>
  <view class="min-h-screen bg-gray-50 pb-[calc(55px+env(safe-area-inset-bottom))]">
    <RecentStudyPopup />
    <GlobalHeader @on-search="handleSearch" :default-keyword="searchValue" />
    <view class="w-full">
      <zb-dropdown-menu class="w-full">
        <zb-dropdown-item
          name="one"
          :options="catalogueTableData"
          v-model="value1"
          @change="getCourseData"
        ></zb-dropdown-item>
        <zb-dropdown-item
          name="two"
          :options="courseTypeData"
          v-model="value2"
          @change="getCourseData"
        ></zb-dropdown-item>
      </zb-dropdown-menu>
    </view>
    <view v-show="courseList.length" class="bg-white p-3 grid grid-cols-2 gap-3">
      <view
        v-for="item in courseList"
        :key="item.courseId"
        class="rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-300"
        @click="jumpTo(item)"
      >
        <view class="relative w-full pb-[56.25%]">
          <image
            class="absolute inset-0 w-full h-full object-cover"
            :src="item.courseImage"
            mode="aspectFill"
          />
        </view>
        <view class="p-2">
          <text class="text-sm text-gray-800 truncate block font-medium">{{
            item.courseName
          }}</text>
        </view>
      </view>
    </view>
    <xw-empty :isShow="!courseList.length" text="暂无课程" textColor="#777777" />
  </view>
</template>

<script>
  import { catalogueType } from "@/utils/constant.js"
  import { getCatalogueList } from "@/api/system/catalogue.js"
  import { listCourse } from "@/api/course"
  import xwEmpty from "@/components/xw-empty/xw-empty"
  import GlobalHeader from "@/components/GlobalHeader/index.vue"
  import RecentStudyPopup from "@/components/RecentStudyPopup/index.vue"
  import { showConfirm } from "@/utils/common.js"
  import tabbarMixin from "@/mixins/tabbar.js"

  export default {
    mixins: [tabbarMixin],

    components: {
      xwEmpty,
      GlobalHeader,
      RecentStudyPopup
    },

    data() {
      return {
        searchValue: "",
        pageNum: 1,
        pageSize: 12,
        dataTotal: 0,
        value1: undefined,
        value2: undefined,
        catalogueTableData: [],
        courseTypeData: [],
        courseList: []
      }
    },

    onLoad(options) {
      // 处理搜索关键词
      if (options && options.keyword) {
        this.searchValue = decodeURIComponent(options.keyword)
        this.getCourseData()
      } else {
        this.getCourseData("onLoad")
      }
      this.fetchCatalogueData()
      this.getCourseTypeList()
    },

    onShow() {
      // 处理tabBar显示
      if (this.$route.query && this.$route.query.showTabBar === "false") {
        uni.hideTabBar()
      }
    },

    onReachBottom() {
      if (this.courseList.length !== this.dataTotal) {
        this.pageNum += 1
        this.getCourseData("onReachBottom")
      }
    },

    onUnload() {
      this.searchValue = ""
      this.pageNum = 1
      this.courseList = []
    },

    methods: {
      // 处理搜索事件
      handleSearch(keyword) {
        if (this.searchValue === keyword) {
          // 如果搜索关键词没有变化，不重新搜索
          return
        }
        this.searchValue = keyword
        this.pageNum = 1 // 重置页码
        this.getCourseData()
      },

      async getCourseData(flag) {
        if (flag !== "onReachBottom") {
          this.pageNum = 1
        }
        try {
          let queryData = {
            pageSize: this.pageSize,
            pageNum: this.pageNum,
            catalogueId: this.value1,
            courseType: this.value2,
            courseName: this.searchValue,
            courseStatus: 2
          }
          const { rows, total } = await listCourse(queryData)
          this.dataTotal = total
          this.courseList = flag === "onReachBottom" ? this.courseList.concat(rows) : rows
        } catch (error) {
          uni.showToast({
            title: "获取课程数据失败",
            icon: "none"
          })
        }
      },

      async fetchCatalogueData() {
        getCatalogueList({ catalogueType: catalogueType.COURSE_CATALOGUE }).then(response => {
          this.catalogueTableData = response.rows.map(({ catalogueId, catalogueName }) => ({
            text: catalogueName,
            value: catalogueId
          }))
          if (this.catalogueTableData[0].text === "全部目录") return
          this.catalogueTableData.unshift({
            text: "全部目录",
            value: undefined
          })
        })
      },

      async getCourseTypeList() {
        this.courseTypeData = await this.$getDict("course_type")
        if (this.courseTypeData[0].text === "全部类型") return
        this.courseTypeData.unshift({
          text: "全部类型",
          value: undefined
        })
      },
      async jumpTo(item) {
        if (item.hasTask) {
          uni.switchTab({ url: "/pages/learn/index" })
        } else {
          uni.navigateTo({
            url: "/pages/course/detail/index?id=" + item.courseId
          })
        }
      }
    }
  }
</script>

<style scoped lang="scss">
  .grid-cols-2 {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
</style>
