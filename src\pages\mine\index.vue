<!--
 * @Description: 个人中心页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-08 15:30:00
-->
<template>
  <view class="flex flex-col min-h-screen bg-gray-100 pb-[calc(55px+env(safe-area-inset-bottom))]">
    <RecentStudyPopup />
    <!-- 顶部个人信息区域 -->
    <view class="bg-[#1e91fc] px-5 pt-6 pb-20">
      <!-- 标题栏 -->
      <view class="text-white text-xl font-medium mb-2">个人中心</view>

      <!-- 用户信息 -->
      <view class="flex items-center justify-between" @click="editProfile">
        <view class="flex items-center">
          <image :src="avatarUrl" class="w-16 h-16 rounded-full bg-white" />
          <view class="ml-4">
            <view class="text-white text-lg font-medium">{{ nickName }}</view>
            <view class="text-white/80 text-sm mt-1">在线学习平台</view>
          </view>
        </view>
        <view class="flex items-center text-white">
          <text class="mr-2">编辑资料</text>
          <view class="i-material-symbols:chevron-right text-xl"></view>
        </view>
      </view>
    </view>

    <!-- 学习数据卡片 -->
    <view class="mx-4 -mt-12 bg-white rounded-xl py-4 px-2 shadow-lg">
      <view class="flex">
        <view class="flex-[2] flex flex-col items-center">
          <view class="text-base mt-1 flex items-center">
            <image
              src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_credit.png"
              class="w-5 h-5 mr-1"
            />
            <view>学分</view>
          </view>
          <view class="text-[#1e91fc] text-2xl font-bold">{{ userExtraInfo.credit || 0 }}</view>
        </view>
        <view class="w-[1px] h-12 bg-gray-300 my-auto border-dashed border-1px"></view>
        <view class="flex-[3] flex flex-col items-center">
          <view class="text-base mt-1 flex items-center mb-1">
            <image
              src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_class_hour.png"
              class="w-5 h-5 mr-1"
            />
            <view>学时</view>
          </view>
          <view class="text-[#1e91fc] text-xl font-bold">
            {{ formatStudyTime(userExtraInfo.classHour) }}
          </view>
        </view>
        <view class="w-[1px] h-12 bg-gray-300 my-auto border-dashed border-1px"></view>
        <view class="flex-[2] flex flex-col items-center">
          <view class="text-base mt-1 flex items-center">
            <image
              src="https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_integral_task.png"
              class="w-5 h-5 mr-1"
            />
            <view>积分</view>
          </view>
          <view class="text-[#1e91fc] text-2xl font-bold">{{ userExtraInfo.integral || 0 }}</view>
        </view>
      </view>
    </view>

    <!-- 最近学习 -->
    <view class="mt-4 px-4" v-if="recentStudy.length">
      <view class="text-lg font-medium mb-3">最近学习</view>
      <scroll-view scroll-x class="whitespace-nowrap">
        <view class="inline-flex gap-3">
          <view
            v-for="(item, index) in recentStudy"
            :key="index"
            class="bg-white rounded-lg p-4 max-w-60 min-w-50 flex-shrink-0 shadow-lg cursor-pointer hover:shadow-xl transition-all duration-300"
            @click="handleStudyItemClick(item)"
          >
            <view class="text-gray-400 text-sm">{{ item.date }}</view>
            <view class="flex items-center mt-2">
              <view
                :class="item.icon"
                class="text-xl flex-shrink-0 mr-2"
                :style="{ color: item.color }"
              ></view>
              <text class="text-base font-medium truncate flex-1" :style="{ color: item.color }">{{
                item.title
              }}</text>
            </view>
            <view class="text-gray-500 text-sm mt-2">
              {{ item.learnType === "1" ? "考试时长: " : "学习时长: " }} {{ item.duration }}</view
            >
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 功能模块入口 -->
    <view class="grid-cols-3 gap-4 px-4 mt-6">
      <view
        v-for="(item, index) in moduleList"
        :key="index"
        class="flex flex-col items-center p-2 cursor-pointer transition-all duration-300 hover:scale-105"
        @click="navigateTo(item.url)"
      >
        <!-- <view
          class="w-14 h-14 rounded-xl bg-gradient-to-br from-white to-gray-50 shadow-lg flex items-center justify-center p-2 transition-all duration-300 hover:shadow-xl"
        > -->
        <image :src="item.src" class="w-14 h-14 transition-transform duration-300" />
        <!-- </view> -->
        <text class="mt-2 text-sm text-gray-700">{{ item.title }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { getUserExtraInfo } from "@/api/system/user"
  import { getRecentStudy } from "@/api/trainingTask"
  import RecentStudyPopup from "@/components/RecentStudyPopup/index.vue"
  import dayjs from "dayjs"
  import { mapState } from "vuex"
  import tabbarMixin from "@/mixins/tabbar.js"

  export default {
    mixins: [tabbarMixin],

    components: {
      RecentStudyPopup
    },
    data() {
      return {
        userExtraInfo: {
          credit: 0,
          classHour: 0,
          integral: 0
        },
        avatarUrl: this.$store.state.user.avatar,
        nickName: this.$store.state.user.nickName,
        moduleList: [
          {
            title: "我的任务",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_task.png",
            url: "/pages/mine/task/index"
          },
          {
            title: "我的课程",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_course.png",
            url: "/pages/mine/my-course/index"
          },
          {
            title: "学习统计",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_statistics.png",
            url: "/pages/mine/statistics/index"
          },
          {
            title: "我的收藏",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_favor.png",
            url: "/pages/mine/my-collect/index"
          },
          {
            title: "我的资料",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_resource.png",
            url: "/pages/home/<USER>/index?from=" + encodeURIComponent("/pages/mine/index")
          },
          {
            title: "我的问卷",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_questionnaire.png",
            url: "/pages/mine/my-questionnaire/index"
          },
          {
            title: "我的荣誉",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_honor.png",
            url: "/pages/mine/honor/index"
          },
          {
            title: "学习轨迹",
            src: "https://training-voc.obs.cn-north-4.myhuaweicloud.com/mine_track.png",
            url: "/pages/mine/track/index"
          }
        ]
      }
    },
    computed: {
      ...mapState({
        recentStudyList: state => state.user.recentStudyList
      }),
      recentStudy() {
        return this.recentStudyList.map(item => ({
          ...item,
          date: this.formatDate(item.learningTime),
          type: this.getTypeByLearnType(item.learnType),
          icon: this.getIconByType(this.getTypeByLearnType(item.learnType)),
          title: item.fieldName,
          duration: this.formatDuration(item.costTime),
          color: this.getColorByType(this.getTypeByLearnType(item.learnType))
        }))
      }
    },
    methods: {
      formatStudyTime(seconds) {
        if (!seconds) return "0小时0分"
        if (seconds < 60) return "1分钟"
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        return hours > 0 ? `${hours}小时${minutes}分钟` : `${minutes}分钟`
      },
      navigateTo(url) {
        uni.navigateTo({ url })
      },
      editProfile() {
        uni.navigateTo({
          url: "/pages/mine/info/index"
        })
      },

      async getUserInfo() {
        try {
          const res = await getUserExtraInfo()
          this.userExtraInfo = {
            credit: res.credit || 0,
            classHour: res.classHour || 0,
            integral: res.integral || 0
          }
        } catch (error) {
          console.error("获取用户信息失败:", error)
        }
      },

      // 根据学习类型获取类型标识
      getTypeByLearnType(learnType) {
        const typeMap = {
          0: "course",
          1: "exam",
          2: "questionnaire"
        }
        return typeMap[learnType] || "course"
      },

      // 根据类型获取图标
      getIconByType(type) {
        const icons = {
          course: "i-material-symbols:school-outline",
          exam: "i-material-symbols:edit-document",
          questionnaire: "i-material-symbols:assignment"
        }
        return icons[type] || icons.course
      },

      // 根据类型获取颜色
      getColorByType(type) {
        const colors = {
          course: "#1e91fc",
          exam: "#25b692",
          questionnaire: "#f59a23"
        }
        return colors[type] || colors.course
      },

      // 格式化时间
      formatDate(date) {
        return date ? dayjs(date).format("YYYY-MM-DD") : "--"
      },

      // 格式化学习时长
      formatDuration(seconds) {
        if (!seconds) return "0分钟"
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        if (hours > 0) {
          return `${hours}小时${minutes > 0 ? minutes + "分" : ""}`
        }
        return `${minutes}分钟`
      },

      // 处理学习项点击
      handleStudyItemClick(item) {
        const urls = {
          course: `/pages/course/detail/index?id=${item.fieldId}`,
          exam: `/pages/mine/exam/prepare/index?baseId=${item.fieldId}&arrangeId=${item.arrangeId}`,
          questionnaire: `/pages/mine/my-questionnaire/doQuestionnaire/index?questionnaireId=${item.fieldId}&questionnaireIssuedId=${item.questionnaireIssuedId}`
        }
        const url = urls[item.type]
        if (url) {
          uni.navigateTo({ url })
        }
      }
    },
    async created() {
      await this.getUserInfo()
      // 只有当 Vuex 中没有数据时才调用接口
      if (!this.recentStudyList.length) {
        await this.$store.dispatch("getRecentStudyData")
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f3f4f6;
  }
</style>
