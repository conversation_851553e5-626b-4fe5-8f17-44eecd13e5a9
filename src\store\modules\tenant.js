/*
 * @Description: 租户信息store
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-24 14:10:36
 * @LastEditTime: 2025-07-17 16:04:07
 */
import { getDomain, setDomain } from "@/utils/auth"
import { querySysTenantInfo } from "@/api/system/login"

const tenant = {
  state: {
    domainName: "pk",
    domainInfo: {},
    startTime: "",
    endTime: ""
  },

  mutations: {
    setDomainName: (state, domainName) => {
      state.domainName = domainName
      setDomain(domainName)
    },
    setDomainInfo: (state, domainInfo) => {
      state.domainInfo = domainInfo
    },
    setStartTime: (state, startTime) => {
      state.startTime = startTime
    },
    setEndTime: (state, endTime) => {
      state.endTime = endTime
    }
  },

  actions: {
    async queryTenantInfo({ commit, state }) {
      const { code, data } = await querySysTenantInfo()
      if (code === 200) {
        commit("setDomainName", data?.domain)
        commit("setDomainInfo", data)
        commit("setStartTime", data?.activityInfo?.startTime)
        commit("setEndTime", data?.activityInfo?.endTime)
      }
    }
  }
}

export default tenant
