/*
 * @Description: tabbar工具函数
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */

import { getTabbarConfig, isTabbarPage } from '@/config/tabbar.js'

/**
 * 为tabbar页面添加底部padding样式
 * @param {string} domainName 租户域名
 */
export function addTabbarPageStyle(domainName) {
  try {
    const config = getTabbarConfig(domainName)
    const tabbarHeight = config.height || '55px'

    // #ifdef H5
    // 创建样式
    const style = document.createElement('style')
    style.id = 'tabbar-page-style'
    style.innerHTML = `
      .tabbar-page-content {
        padding-bottom: calc(${tabbarHeight} + env(safe-area-inset-bottom)) !important;
      }
    `

    // 移除旧样式
    const oldStyle = document.getElementById('tabbar-page-style')
    if (oldStyle) {
      oldStyle.remove()
    }

    // 添加新样式
    document.head.appendChild(style)
    // #endif
  } catch (error) {
    console.error('添加tabbar页面样式失败:', error)
  }
}

/**
 * 检查当前页面是否需要显示tabbar
 * @param {string} route 当前路由
 * @param {string} domainName 租户域名
 * @returns {boolean}
 */
export function shouldShowTabbar(route, domainName) {
  return isTabbarPage(route, domainName)
}

/**
 * 初始化tabbar相关设置
 * @param {string} domainName 租户域名
 */
export function initTabbar(domainName) {
  try {
    console.log('初始化tabbar设置, domainName:', domainName)
    // #ifdef H5
    addTabbarPageStyle(domainName)
    // #endif
  } catch (error) {
    console.error('初始化tabbar失败:', error)
  }
}

export default {
  addTabbarPageStyle,
  shouldShowTabbar,
  initTabbar
}
